---
# Github Actions release for rclone
# -*- compile-command: "yamllint -f parsable build_publish_docker_plugin.yml" -*-

name: Release Build for Docker Plugin

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      manual:
        description: Manual run (bypass default conditions)
        type: boolean
        default: true

jobs:
  build_docker_volume_plugin:
    if: inputs.manual || github.repository == 'rclone/rclone'
    name: Build docker plugin job
    runs-on: ubuntu-latest
    steps:
      - name: Free some space
        shell: bash
        run: |
          df -h .
          # Remove android SDK
          sudo rm -rf /usr/local/lib/android || true
          # Remove .net runtime
          sudo rm -rf /usr/share/dotnet || true
          df -h .
      - name: Checkout master
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Build and publish docker plugin
        shell: bash
        run: |
          VER=${GITHUB_REF#refs/tags/}
          PLUGIN_USER=rclone
          docker login --username ${{ secrets.DOCKER_HUB_USER }} \
                       --password-stdin <<< "${{ secrets.DOCKER_HUB_PASSWORD }}"
          for PLUGIN_ARCH in amd64 arm64 arm/v7 arm/v6 ;do
              export PLUGIN_USER PLUGIN_ARCH
              make docker-plugin PLUGIN_TAG=${PLUGIN_ARCH/\//-}
              make docker-plugin PLUGIN_TAG=${PLUGIN_ARCH/\//-}-${VER#v}
          done
          make docker-plugin PLUGIN_ARCH=amd64 PLUGIN_TAG=latest
          make docker-plugin PLUGIN_ARCH=amd64 PLUGIN_TAG=${VER#v}
