---
# Github Actions build for rclone
# -*- compile-command: "yamllint -f parsable build.yml" -*-

name: build
# Trigger the workflow on push or pull request
on:
  push:
    branches:
      - '**'
    tags:
      - '**'
  pull_request:
  workflow_dispatch:
    inputs:
      manual:
        description: Manual run (bypass default conditions)
        type: boolean
        default: true

jobs:
  create-release:
    runs-on: ubuntu-latest
    if: github.head_ref == '' && github.repository == 'jonntd/rclone'
    outputs:
      upload_url: ${{ steps.set_upload_url.outputs.upload_url }}
    steps:
      - 
        name: Create Release
        if: ${{ contains(github.ref, 'refs/tags/') }}
        id: new_release
        uses: ncipollo/release-action@v1
        with:
          tag: ${{ github.ref }}
          name: rclone ${{ github.ref_name }}
          body: |
            ## What's Changed

            ## How to Update
            ```bash
            curl -fsSL https://raw.githubusercontent.com/jonntd/rclone/master-115/install.sh | sudo bash 
            ```
          draft: true
          prerelease: false
      - 
        name: Set Upload URL
        id: set_upload_url
        run: |
          if [ "${{ contains(github.ref, 'refs/tags/') }}" = "true" ]; then
            echo "upload_url=${{ steps.new_release.outputs.upload_url }}" >> "$GITHUB_OUTPUT"
          fi
  build:
    needs:
      - create-release
    if: ${{ inputs.manual || (github.repository == 'jonntd/rclone' && (github.event_name != 'pull_request' || github.event.pull_request.head.repo.full_name != github.event.pull_request.base.repo.full_name)) }}
    timeout-minutes: 60
    strategy:
      fail-fast: false
      matrix:
        job_name: ['linux', 'linux_386', 'mac_amd64', 'mac_arm64', 'windows']

        include:
          - job_name: linux
            os: ubuntu-latest
            go: '>=1.24.0-rc.1'
            gotags: cmount,noselfupdate
            build_flags: '-include "^linux/"'
            check: true
            quicktest: true
            racequicktest: true
            librclonetest: true
            deploy: true

          - job_name: linux_386
            os: ubuntu-latest
            go: '>=1.24.0-rc.1'
            goarch: 386
            gotags: cmount,noselfupdate
            quicktest: true

          - job_name: mac_amd64
            os: macos-latest
            go: '>=1.24.0-rc.1'
            gotags: 'cmount,noselfupdate'
            build_flags: '-include "^darwin/amd64" -cgo'
            quicktest: true
            racequicktest: true
            deploy: true

          - job_name: mac_arm64
            os: macos-latest
            go: '>=1.24.0-rc.1'
            gotags: 'cmount,noselfupdate'
            build_flags: '-include "^darwin/arm64" -cgo -macos-arch arm64 -cgo-cflags=-I/usr/local/include -cgo-ldflags=-L/usr/local/lib'
            deploy: true

          - job_name: windows
            os: windows-latest
            go: '>=1.24.0-rc.1'
            gotags: cmount,noselfupdate
            cgo: '0'
            build_flags: '-include "^windows/"'
            build_args: '-buildmode exe'
            quicktest: true
            deploy: true

          # - job_name: other_os
          #   os: ubuntu-latest
          #   go: '>=1.24.0-rc.1'
          #   build_flags: '-exclude "^(windows/|darwin/|linux/)"'
          #   compile_all: true
          #   deploy: true

          # - job_name: go1.23
          #   os: ubuntu-latest
          #   go: '1.23'
          #   quicktest: true
          #   racequicktest: true

    name: ${{ matrix.job_name }}

    runs-on: ${{ matrix.os }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ matrix.go }}
          check-latest: true

      - name: Install purego dependency
        shell: bash
        run: |
          go get github.com/ebitengine/purego@5047c08

      - name: Set environment variables
        shell: bash
        run: |
          echo 'GOTAGS=${{ matrix.gotags }}' >> $GITHUB_ENV
          echo 'BUILD_FLAGS=${{ matrix.build_flags }}' >> $GITHUB_ENV
          echo 'BUILD_ARGS=${{ matrix.build_args }}' >> $GITHUB_ENV
          if [[ "${{ matrix.goarch }}" != "" ]]; then echo 'GOARCH=${{ matrix.goarch }}' >> $GITHUB_ENV ; fi
          if [[ "${{ matrix.cgo }}" != "" ]]; then echo 'CGO_ENABLED=${{ matrix.cgo }}' >> $GITHUB_ENV ; fi

      - name: Install Libraries on Linux
        shell: bash
        run: |
          sudo modprobe fuse
          sudo chmod 666 /dev/fuse
          sudo chown root:$USER /etc/fuse.conf
          sudo apt-get update
          sudo apt-get install -y fuse3 libfuse-dev rpm pkg-config git-annex git-annex-remote-rclone nfs-common
        if: matrix.os == 'ubuntu-latest'

      - name: Install Libraries on macOS
        shell: bash
        run: |
          # https://github.com/Homebrew/brew/issues/15621#issuecomment-1619266788
          # https://github.com/orgs/Homebrew/discussions/4612#discussioncomment-6319008
          unset HOMEBREW_NO_INSTALL_FROM_API
          brew untap --force homebrew/core
          brew untap --force homebrew/cask
          brew update
          brew install --cask macfuse
          brew install git-annex git-annex-remote-rclone
        if: matrix.os == 'macos-latest'

      - name: Install Libraries on Windows
        shell: powershell
        run: |
          $ProgressPreference = 'SilentlyContinue'
          choco install -y winfsp zip
          echo "CPATH=C:\Program Files\WinFsp\inc\fuse;C:\Program Files (x86)\WinFsp\inc\fuse" | Out-File -FilePath $env:GITHUB_ENV -Encoding utf8 -Append
          if ($env:GOARCH -eq "386") {
            choco install -y mingw --forcex86 --force
            echo "C:\\ProgramData\\chocolatey\\lib\\mingw\\tools\\install\\mingw32\\bin" | Out-File -FilePath $env:GITHUB_PATH -Encoding utf8 -Append
          }
          # Copy mingw32-make.exe to make.exe so the same command line
          # can be used on Windows as on macOS and Linux
          $path = (get-command mingw32-make.exe).Path
          Copy-Item -Path $path -Destination (Join-Path (Split-Path -Path $path) 'make.exe')
        if: matrix.os == 'windows-latest'

      - name: Print Go version and environment
        shell: bash
        run: |
          printf "Using go at: $(which go)\n"
          printf "Go version: $(go version)\n"
          printf "\n\nGo environment:\n\n"
          go env
          printf "\n\nRclone environment:\n\n"
          make vars
          printf "\n\nSystem environment:\n\n"
          env

      - name: Build rclone
        shell: bash
        run: |
          make

      - name: Rclone version
        shell: bash
        run: |
          rclone version
      # - name: Run tests
      #   shell: bash
      #   run: |
      #     make quicktest
      #   if: matrix.quicktest

      # - name: Race test
      #   shell: bash
      #   run: |
      #     make racequicktest
      #   if: matrix.racequicktest

      # - name: Run librclone tests
      #   shell: bash
      #   run: |
      #     make -C librclone/ctest test
      #     make -C librclone/ctest clean
      #     librclone/python/test_rclone.py
      #   if: matrix.librclonetest

      - name: Compile all architectures test
        shell: bash
        run: |
          make
          make compile_all
        if: matrix.compile_all

      - name: Deploy built binaries
        shell: bash
        run: |
          if [[ "${{ matrix.os }}" == "ubuntu-latest" ]]; then make release_dep_linux ; fi
          make ci_beta
        env:
          RCLONE_CONFIG_PASS: ${{ secrets.RCLONE_CONFIG_PASS }}
        # working-directory: '$(modulePath)'
        # Deploy binaries if enabled in config && not a PR && not a fork
        if: matrix.deploy && github.head_ref == '' && github.repository == 'jonntd/rclone'
      - 
        if: ${{ contains(github.ref, 'refs/tags/') }}
        name: Upload Release Artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.job_name }}
          if-no-files-found: ignore
          path: |
            build/
      - 
        if: ${{ ! contains(github.ref, 'refs/tags/') }}
        name: Upload Test Artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.job_name }}
          if-no-files-found: ignore
          path: |
            build/*amd64.zip
            build/*arm64.zip

  # lint:
  #   if: inputs.manual || (github.repository == 'jonntd/rclone' && (github.event_name != 'pull_request' || github.event.pull_request.head.repo.full_name != github.event.pull_request.base.repo.full_name))
  #   timeout-minutes: 30
  #   name: "lint"
  #   runs-on: ubuntu-latest

  #   steps:
  #     - name: Get runner parameters
  #       id: get-runner-parameters
  #       shell: bash
  #       run: |
  #         echo "year-week=$(/bin/date -u "+%Y%V")" >> $GITHUB_OUTPUT
  #         echo "runner-os-version=$ImageOS" >> $GITHUB_OUTPUT

  #     - name: Checkout
  #       uses: actions/checkout@v4

  #     - name: Install Go
  #       id: setup-go
  #       uses: actions/setup-go@v5
  #       with:
  #         go-version: '>=1.23.0-rc.1'
  #         check-latest: true
  #         cache: false

  #     - name: Cache
  #       uses: actions/cache@v4
  #       with:
  #         path: |
  #           ~/go/pkg/mod
  #           ~/.cache/go-build
  #           ~/.cache/golangci-lint
  #         key: golangci-lint-${{ steps.get-runner-parameters.outputs.runner-os-version }}-go${{ steps.setup-go.outputs.go-version }}-${{ steps.get-runner-parameters.outputs.year-week }}-${{ hashFiles('go.sum') }}
  #         restore-keys: golangci-lint-${{ steps.get-runner-parameters.outputs.runner-os-version }}-go${{ steps.setup-go.outputs.go-version }}-${{ steps.get-runner-parameters.outputs.year-week }}-

  #     - name: Code quality test (Linux)
  #       uses: golangci/golangci-lint-action@v6
  #       with:
  #         version: latest
  #         skip-cache: true

  #     - name: Code quality test (Windows)
  #       uses: golangci/golangci-lint-action@v6
  #       env:
  #         GOOS: "windows"
  #       with:
  #         version: latest
  #         skip-cache: true

  #     - name: Code quality test (macOS)
  #       uses: golangci/golangci-lint-action@v6
  #       env:
  #         GOOS: "darwin"
  #       with:
  #         version: latest
  #         skip-cache: true

  #     - name: Code quality test (FreeBSD)
  #       uses: golangci/golangci-lint-action@v6
  #       env:
  #         GOOS: "freebsd"
  #       with:
  #         version: latest
  #         skip-cache: true

  #     - name: Code quality test (OpenBSD)
  #       uses: golangci/golangci-lint-action@v6
  #       env:
  #         GOOS: "openbsd"
  #       with:
  #         version: latest
  #         skip-cache: true

  #     - name: Install govulncheck
  #       run: go install golang.org/x/vuln/cmd/govulncheck@latest

  #     - name: Scan for vulnerabilities
  #       run: govulncheck ./...

      - name: Scan edits of autogenerated files
        run: bin/check_autogenerated_edits.py
        if: github.event_name == 'pull_request'

  android:
    if: inputs.manual || (github.repository == 'rclone/rclone' && (github.event_name != 'pull_request' || github.event.pull_request.head.repo.full_name != github.event.pull_request.base.repo.full_name))
    timeout-minutes: 30
    name: "android-all"
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # Upgrade together with NDK version
      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '>=1.24.0-rc.1'

      - name: Set global environment variables
        shell: bash
        run: |
          echo "VERSION=$(make version)" >> $GITHUB_ENV

      - name: build native rclone
        run: |
          make

      - name: install gomobile
        run: |
          go install golang.org/x/mobile/cmd/gobind@latest
          go install golang.org/x/mobile/cmd/gomobile@latest
          env PATH=$PATH:~/go/bin gomobile init
          echo "RCLONE_NDK_VERSION=21" >> $GITHUB_ENV

      - name: arm-v7a gomobile build
        run: env PATH=$PATH:~/go/bin gomobile bind -androidapi ${RCLONE_NDK_VERSION} -v -target=android/arm -javapkg=org.rclone -ldflags '-s -X github.com/rclone/rclone/fs.Version='${VERSION} github.com/rclone/rclone/librclone/gomobile

      - name: arm-v7a Set environment variables
        shell: bash
        run: |
          echo "CC=$(echo $ANDROID_NDK/toolchains/llvm/prebuilt/linux-x86_64/bin/armv7a-linux-androideabi${RCLONE_NDK_VERSION}-clang)" >> $GITHUB_ENV
          echo "CC_FOR_TARGET=$CC" >> $GITHUB_ENV
          echo 'GOOS=android' >> $GITHUB_ENV
          echo 'GOARCH=arm' >> $GITHUB_ENV
          echo 'GOARM=7' >> $GITHUB_ENV
          echo 'CGO_ENABLED=1' >> $GITHUB_ENV
          echo 'CGO_LDFLAGS=-fuse-ld=lld -s -w' >> $GITHUB_ENV

      - name: arm-v7a build
        run: go build -v -tags android -trimpath -ldflags '-s -X github.com/rclone/rclone/fs.Version='${VERSION} -o build/rclone-android-${RCLONE_NDK_VERSION}-armv7a .

      - name: arm64-v8a Set environment variables
        shell: bash
        run: |
          echo "CC=$(echo $ANDROID_NDK/toolchains/llvm/prebuilt/linux-x86_64/bin/aarch64-linux-android${RCLONE_NDK_VERSION}-clang)" >> $GITHUB_ENV
          echo "CC_FOR_TARGET=$CC" >> $GITHUB_ENV
          echo 'GOOS=android' >> $GITHUB_ENV
          echo 'GOARCH=arm64' >> $GITHUB_ENV
          echo 'CGO_ENABLED=1' >> $GITHUB_ENV
          echo 'CGO_LDFLAGS=-fuse-ld=lld -s -w' >> $GITHUB_ENV

      - name: arm64-v8a build
        run: go build -v -tags android -trimpath -ldflags '-s -X github.com/rclone/rclone/fs.Version='${VERSION} -o build/rclone-android-${RCLONE_NDK_VERSION}-armv8a .

      - name: x86 Set environment variables
        shell: bash
        run: |
          echo "CC=$(echo $ANDROID_NDK/toolchains/llvm/prebuilt/linux-x86_64/bin/i686-linux-android${RCLONE_NDK_VERSION}-clang)" >> $GITHUB_ENV
          echo "CC_FOR_TARGET=$CC" >> $GITHUB_ENV
          echo 'GOOS=android' >> $GITHUB_ENV
          echo 'GOARCH=386' >> $GITHUB_ENV
          echo 'CGO_ENABLED=1' >> $GITHUB_ENV
          echo 'CGO_LDFLAGS=-fuse-ld=lld -s -w' >> $GITHUB_ENV

      - name: x86 build
        run: go build -v -tags android -trimpath -ldflags '-s -X github.com/rclone/rclone/fs.Version='${VERSION} -o build/rclone-android-${RCLONE_NDK_VERSION}-x86 .

      - name: x64 Set environment variables
        shell: bash
        run: |
          echo "CC=$(echo $ANDROID_NDK/toolchains/llvm/prebuilt/linux-x86_64/bin/x86_64-linux-android${RCLONE_NDK_VERSION}-clang)" >> $GITHUB_ENV
          echo "CC_FOR_TARGET=$CC" >> $GITHUB_ENV
          echo 'GOOS=android' >> $GITHUB_ENV
          echo 'GOARCH=amd64' >> $GITHUB_ENV
          echo 'CGO_ENABLED=1' >> $GITHUB_ENV
          echo 'CGO_LDFLAGS=-fuse-ld=lld -s -w' >> $GITHUB_ENV

      - name: x64 build
        run: go build -v -tags android -trimpath -ldflags '-s -X github.com/rclone/rclone/fs.Version='${VERSION} -o build/rclone-android-${RCLONE_NDK_VERSION}-x64 .

      - name: Upload artifacts
        run: |
          make ci_upload
        env:
          RCLONE_CONFIG_PASS: ${{ secrets.RCLONE_CONFIG_PASS }}
        # Upload artifacts if not a PR && not a fork
        if: env.RCLONE_CONFIG_PASS != '' && github.head_ref == '' && github.repository == 'rclone/rclone'

  termux:
    needs:
      - create-release
    timeout-minutes: 60
    runs-on: ubuntu-latest
    if: github.head_ref == '' && github.repository == 'jonntd/rclone'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set Variables (beta)
        if: github.event_name == 'push' && contains(github.ref, 'refs/heads/')
        shell: bash
        run: |
          LAST_TAG=$(git describe --tags --abbrev=0)
          echo "version=$(echo $LAST_TAG | cut -c 2- | cut -d- -f1)" >> $GITHUB_ENV
          echo "revision=0" >> $GITHUB_ENV
          echo "branch=$(git rev-parse --abbrev-ref HEAD)" >> $GITHUB_ENV
          echo "version_all=v$(echo $LAST_TAG | cut -c 2- | cut -d- -f1)-beta.$(git rev-list --count HEAD).$(git show --no-patch --no-notes --pretty='%h' HEAD).$(git rev-parse --abbrev-ref HEAD)" >> $GITHUB_ENV
      - name: Set Variables (release)
        if: github.event_name == 'push' && contains(github.ref, 'refs/tags/')
        shell: bash
        run: |
          LAST_TAG=$(git describe --tags --abbrev=0)
          echo "version=$(echo $LAST_TAG | cut -c 2- | cut -d- -f1)" >> $GITHUB_ENV
          echo "revision=$(echo $LAST_TAG | cut -c 2- | cut -d- -f2)" >> $GITHUB_ENV
          echo "branch=$LAST_TAG" >> $GITHUB_ENV
          echo "version_all=$LAST_TAG" >> $GITHUB_ENV
      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: 'termux/termux-packages'
      - name: build (beta)
        if: github.event_name == 'push' && contains(github.ref, 'refs/heads/')
        shell: bash
        run: |
          sed -i -r "s/^TERMUX_PKG_MAINTAINER=(.*)$/TERMUX_PKG_MAINTAINER=\"@jonntd\"/g" packages/rclone/build.sh
          sed -i -r "s/^TERMUX_PKG_VERSION=(.*)$/TERMUX_PKG_VERSION=\"${{ env.version }}\"/g" packages/rclone/build.sh
          grep -q "^TERMUX_PKG_REVISION" packages/rclone/build.sh || sed -i "/^TERMUX_PKG_VERSION=/a TERMUX_PKG_REVISION=0" packages/rclone/build.sh
          sed -i -r "s/^TERMUX_PKG_REVISION=(.*)$/TERMUX_PKG_REVISION=${{ env.revision }}/g" packages/rclone/build.sh
          sed -i -r "s/^TERMUX_PKG_SRCURL=(.*)$/TERMUX_PKG_SRCURL=git+https:\/\/github.com\/jonntd\/rclone/g" packages/rclone/build.sh
          sed -i -r "s/^TERMUX_PKG_SHA256=(.*)$/TERMUX_PKG_GIT_BRANCH=${{ env.branch }}/g" packages/rclone/build.sh
          sed -i -r "s/-ldflags \"(.*)\"/-ldflags \"-s -X github.com\/rclone\/rclone\/fs.Version=${{ env.version_all }}\"/g" packages/rclone/build.sh
          sed -i -r "s/cp rclone.1 (.*)/cp rclone.1 \1\n\tzip \$HOME\/termux-packages\/output\/rclone-${{ env.version_all }}-termux-\$TERMUX_ARCH.zip rclone/g" packages/rclone/build.sh
          cd ./scripts
          ./run-docker.sh ./build-package.sh -a aarch64 rclone
          ./run-docker.sh ./build-package.sh -a arm rclone
          cd ../output
          for file in *.deb; do mvto=$(sed -r "s/^rclone_(.*)_(.*)$/rclone-${{ env.version_all }}-termux-\2/g" <<< ${file}); mv "$file" "$mvto"; done
      - name: build (release)
        if: github.event_name == 'push' && contains(github.ref, 'refs/tags/')
        shell: bash
        run: |
          sed -i -r "s/^TERMUX_PKG_MAINTAINER=(.*)$/TERMUX_PKG_MAINTAINER=\"@jonntd\"/g" packages/rclone/build.sh
          sed -i -r "s/^TERMUX_PKG_VERSION=(.*)$/TERMUX_PKG_VERSION=\"${{ env.version }}\"/g" packages/rclone/build.sh
          grep -q "^TERMUX_PKG_REVISION" packages/rclone/build.sh || sed -i "/^TERMUX_PKG_VERSION=/a TERMUX_PKG_REVISION=0" packages/rclone/build.sh
          sed -i -r "s/^TERMUX_PKG_REVISION=(.*)$/TERMUX_PKG_REVISION=${{ env.revision }}/g" packages/rclone/build.sh
          sed -i -r "s/^TERMUX_PKG_SRCURL=(.*)$/TERMUX_PKG_SRCURL=git+https:\/\/github.com\/jonntd\/rclone/g" packages/rclone/build.sh
          sed -i -r "s/^TERMUX_PKG_SHA256=(.*)$/TERMUX_PKG_GIT_BRANCH=${{ env.branch }}/g" packages/rclone/build.sh
          sed -i -r "s/-ldflags \"(.*)\"/-ldflags \"-s -X github.com\/rclone\/rclone\/fs.Version=${{ env.version_all }}\"/g" packages/rclone/build.sh
          sed -i -r "s/cp rclone.1 (.*)/cp rclone.1 \1\n\tzip \$HOME\/termux-packages\/output\/rclone-${{ env.version_all }}-termux-\$TERMUX_ARCH.zip rclone/g" packages/rclone/build.sh
          cd ./scripts
          ./run-docker.sh ./build-package.sh -a aarch64 rclone
          ./run-docker.sh ./build-package.sh -a arm rclone
          cd ../output
          for file in *.deb; do mvto=$(sed -r "s/^rclone_(.*)_(.*)$/rclone-${{ env.version_all }}-termux-\2/g" <<< ${file}); mv "$file" "$mvto"; done
      # - 
      #   if: ${{ contains(github.ref, 'refs/tags/') }}
      #   name: Upload Assets to Release
      #   shell: bash
      #   run: |
      #     UPLOAD_URL="$(echo "${{ needs.create-release.outputs.upload_url }}" | cut -d'{' -f1)"
      #     for FILE in output/*; do
      #       [ ! -f "$FILE" ] && continue
      #       echo "Uploading \"$FILE\"..."
      #       curl -sX POST \
      #         --output /dev/null \
      #         -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
      #         -H "Accept: application/vnd.github.v3+json" \
      #         -H "Content-Type: $(file -b --mime-type ${FILE})" \
      #         -H "Content-Length: $(wc -c <${FILE} | xargs)" \
      #         -T "${FILE}" \
      #         "${UPLOAD_URL}?name=$(basename ${FILE})"
      #     done
      - 
        if: ${{ contains(github.ref, 'refs/tags/') }}
        name: Upload Release Artifact
        uses: actions/upload-artifact@v4
        with:
          name: termux
          if-no-files-found: ignore
          path: |
            output/
      - 
        if: ${{ ! contains(github.ref, 'refs/tags/') }}
        name: Upload Test Artifact
        uses: actions/upload-artifact@v4
        with:
          name: termux
          if-no-files-found: ignore
          path: |
            output/*.zip

  upload-release:
    if: ${{ contains(github.ref, 'refs/tags/') }}
    needs: [create-release, build, termux]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Download Artifacts
        uses: actions/download-artifact@v4
        with:
          path: build/
          merge-multiple: true
      - name: Checksums
        shell: bash
        run: |
          cd build || exit 1
          md5sum rclone-v* > MD5SUMS
          sha1sum rclone-v* > SHA1SUMS
          sha256sum rclone-v* > SHA256SUMS
      - 
        name: Upload Assets to Release
        uses: shogo82148/actions-upload-release-asset@v1
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: 'build/*'
