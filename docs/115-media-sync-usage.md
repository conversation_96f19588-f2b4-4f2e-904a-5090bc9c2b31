# 115网盘 Media-Sync 功能使用指南

## 概述

115网盘后端现在支持 `media-sync` backend command，可以将网盘中的视频文件同步到本地目录，并创建对应的 `.strm` 文件。这个功能特别适用于媒体库管理，如 Plex、Jellyfin、Emby 等媒体服务器。

## 功能特点

- ✅ **智能文件过滤**：自动识别视频文件，支持自定义扩展名
- ✅ **优化的 .strm 内容**：使用 115网盘的 pick_code 格式 (`115://pick_code`)
- ✅ **递归目录处理**：自动处理子目录结构
- ✅ **大小过滤**：可设置最小文件大小，跳过小文件
- ✅ **预览模式**：支持 dry-run 预览将要创建的文件
- ✅ **详细统计**：提供完整的处理统计信息
- ✅ **pick_code 自动获取**：智能获取文件的 pick_code

## 基本用法

### 命令格式
```bash
rclone backend media-sync 115:source_path target_path [options]
```

### 基本示例
```bash
# 同步电影目录
rclone backend media-sync 115:Movies /local/media/movies

# 同步电视剧目录
rclone backend media-sync 115:TVShows /local/media/tvshows

# 使用选项指定目标路径
rclone backend media-sync 115:Videos -o target-path=/local/media/videos
```

## 高级选项

### 文件大小过滤
```bash
# 只处理大于 200MB 的文件
rclone backend media-sync 115:Movies /local/media/movies -o min-size=200M

# 只处理大于 1GB 的文件
rclone backend media-sync 115:Movies /local/media/movies -o min-size=1G
```

### 文件类型过滤
```bash
# 只包含特定格式
rclone backend media-sync 115:Movies /local/media/movies -o include=mp4,mkv,avi

# 排除特定格式
rclone backend media-sync 115:Movies /local/media/movies -o exclude=wmv,flv
```

### .strm 文件格式
```bash
# 使用 pick_code 格式（推荐，默认）
rclone backend media-sync 115:Movies /local/media/movies -o strm-format=pickcode

# 使用文件路径格式（兼容模式）
rclone backend media-sync 115:Movies /local/media/movies -o strm-format=path
```

### 预览模式
```bash
# 预览将要创建的文件，不实际创建
rclone backend media-sync 115:Movies /local/media/movies -o dry-run=true
```

## 完整选项列表

| 选项 | 默认值 | 说明 |
|------|--------|------|
| `min-size` | `100M` | 最小文件大小过滤 |
| `strm-format` | `pickcode` | .strm文件内容格式：`pickcode`/`path` |
| `include` | `mp4,mkv,avi,mov,wmv,flv,webm,m4v,3gp,ts,m2ts` | 包含的文件扩展名 |
| `exclude` | _(空)_ | 排除的文件扩展名 |
| `dry-run` | `false` | 预览模式，不实际创建文件 |
| `target-path` | _(空)_ | 目标路径（如果不在参数中指定） |

## 115网盘特色功能

### pick_code 优势
115网盘的 pick_code 是文件的唯一标识符，具有以下优势：
- **永久有效**：不会因为文件移动而失效
- **直接访问**：可以直接通过 pick_code 访问文件
- **高效播放**：媒体服务器可以直接使用 pick_code 播放

### 智能 pick_code 获取
```bash
# 系统会自动尝试多种方式获取 pick_code：
# 1. 从文件对象直接获取（最快）
# 2. 通过 GetPickCodeByPath API 获取
# 3. 回退到文件路径模式（兼容性）
```

## 使用场景

### 1. Plex 媒体库
```bash
# 同步电影
rclone backend media-sync 115:Movies /var/lib/plexmediaserver/Movies -o min-size=500M

# 同步电视剧
rclone backend media-sync 115:TVShows /var/lib/plexmediaserver/TVShows -o min-size=100M
```

### 2. Jellyfin 媒体库
```bash
# 同步到 Jellyfin 目录
rclone backend media-sync 115:Media /var/lib/jellyfin/media -o strm-format=pickcode
```

### 3. 定期同步脚本
```bash
#!/bin/bash
# sync-115-media.sh

echo "开始同步115网盘媒体库..."

# 同步电影
echo "同步电影..."
rclone backend media-sync 115:Movies /media/movies -o min-size=500M

# 同步电视剧
echo "同步电视剧..."
rclone backend media-sync 115:TVShows /media/tvshows -o min-size=100M

# 同步纪录片
echo "同步纪录片..."
rclone backend media-sync 115:Documentaries /media/documentaries -o min-size=200M

echo "同步完成！"
```

## 输出示例

### 成功执行
```
2024/01/15 10:30:00 INFO  : 🎬 开始115网盘媒体库同步...
2024/01/15 10:30:00 INFO  : 📋 同步参数: 源=Movies, 目标=/local/media/movies, 最小大小=100M, 格式=pickcode, 预览=false
2024/01/15 10:30:01 INFO  : 📁 处理目录: Movies -> /local/media/movies
2024/01/15 10:30:02 INFO  : ✅ 创建.strm文件: /local/media/movies/Action/Movie1.strm (大小: 2.5G, 内容: 115://abc123def456)
2024/01/15 10:30:03 INFO  : ✅ 创建.strm文件: /local/media/movies/Comedy/Movie2.strm (大小: 1.8G, 内容: 115://xyz789uvw012)
2024/01/15 10:30:05 INFO  : 🎉 媒体同步完成! 处理目录:3, 处理文件:25, 创建.strm:20, 跳过:5, 错误:0
```

### pick_code 获取过程
```
2024/01/15 10:30:02 DEBUG : 📁 处理目录: Movies/Action -> /local/media/movies/Action
2024/01/15 10:30:02 DEBUG : 🔍 获取pick_code: Movie1.mp4
2024/01/15 10:30:02 INFO  : ✅ 创建.strm文件: /local/media/movies/Action/Movie1.strm (大小: 2.5G, 内容: 115://abc123def456)
```

### 回退到路径模式
```
2024/01/15 10:30:03 DEBUG : ⚠️ 无法获取pick_code，使用路径模式: Movie2.mp4
2024/01/15 10:30:03 INFO  : ✅ 创建.strm文件: /local/media/movies/Movie2.strm (大小: 1.8G, 内容: Movies/Movie2.mp4)
```

## 返回的统计信息

命令执行后会返回 JSON 格式的统计信息：

```json
{
  "processed_dirs": 3,
  "processed_files": 25,
  "created_strm": 20,
  "skipped_files": 5,
  "errors": 0,
  "error_messages": [],
  "dry_run": false
}
```

## 注意事项

1. **Cookie 有效性**：确保 115网盘的 Cookie 有效且未过期
2. **API 限制**：115网盘有 QPS 限制，大量文件处理时会自动控制速度
3. **pick_code 获取**：某些文件可能无法获取 pick_code，会自动回退到路径模式
4. **网络稳定性**：建议在网络稳定的环境下执行
5. **存储空间**：.strm 文件很小，但仍需要确保有足够的存储空间

## 故障排除

### 常见问题

1. **Cookie 过期**
   ```
   Error: 401 Unauthorized
   ```
   解决：更新 115网盘的 Cookie 配置

2. **QPS 限制**
   ```
   Error: 429 Too Many Requests
   ```
   解决：系统会自动重试，或增加 `--115-pacer-min-sleep` 参数

3. **无法获取 pick_code**
   ```
   WARN: ⚠️ 无法获取pick_code，使用路径模式: filename.mp4
   ```
   解决：这是正常的回退机制，不影响功能

### 调试模式
```bash
# 启用详细日志
rclone backend media-sync 115:Movies /local/media/movies -v

# 启用调试日志
rclone backend media-sync 115:Movies /local/media/movies -vv
```

## 与 123网盘的对比

| 特性 | 123网盘 | 115网盘 | 说明 |
|------|---------|---------|------|
| 文件标识符 | fileId | pick_code | 115的pick_code更稳定 |
| .strm 格式 | `123://fileId` | `115://pick_code` | 不同的协议前缀 |
| API 限制 | 相对宽松 | 较严格的QPS限制 | 115需要更保守的调用频率 |
| 获取方式 | 直接从对象 | 多重回退机制 | 115有更复杂的获取逻辑 |
| 稳定性 | 高 | 高 | 两者都很稳定 |

## 最佳实践

1. **批量处理**：对于大量文件，建议分批处理
2. **定期同步**：设置定时任务定期同步新文件
3. **监控日志**：关注 pick_code 获取失败的情况
4. **备份配置**：定期备份 rclone 配置文件
5. **测试环境**：先在小范围测试，确认效果后再大规模使用

这个新的 `media-sync` 功能为 115网盘用户提供了强大的媒体库管理能力，特别是 pick_code 的使用让 .strm 文件更加稳定和高效。
