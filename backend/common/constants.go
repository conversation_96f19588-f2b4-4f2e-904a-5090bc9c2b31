package common

import "time"

// 通用常量定义
// 统一123网盘和115网盘中重复的常量定义

// 重试相关常量
const (
	// DefaultMaxRetries 默认最大重试次数
	DefaultMaxRetries = 4
	
	// DefaultRetryDelay 默认重试延迟
	DefaultRetryDelay = time.Second
	
	// DefaultRetryBackoffMultiplier 默认重试退避倍数
	DefaultRetryBackoffMultiplier = 2
)

// 文件大小相关常量
const (
	// DefaultChunkSize 默认分片大小 (100MB)
	DefaultChunkSize = 100 * 1024 * 1024
	
	// MinChunkSize 最小分片大小 (5MB)
	MinChunkSize = 5 * 1024 * 1024
	
	// MaxChunkSize 最大分片大小 (500MB)
	MaxChunkSize = 500 * 1024 * 1024
	
	// SmallFileThreshold 小文件阈值 (1MB)
	SmallFileThreshold = 1024 * 1024
	
	// LargeFileThreshold 大文件阈值 (100MB)
	LargeFileThreshold = 100 * 1024 * 1024
)

// 内存管理相关常量
const (
	// DefaultMemoryBufferSize 默认内存缓冲区大小 (50MB)
	DefaultMemoryBufferSize = 50 * 1024 * 1024
	
	// DefaultIOBufferSize 默认IO缓冲区大小 (64KB)
	DefaultIOBufferSize = 64 * 1024
	
	// MaxMemoryUsageThreshold 最大内存使用阈值 (200MB)
	MaxMemoryUsageThreshold = 200 * 1024 * 1024
)

// 网络相关常量
const (
	// DefaultConnectTimeout 默认连接超时
	DefaultConnectTimeout = 10 * time.Second
	
	// DefaultReadWriteTimeout 默认读写超时
	DefaultReadWriteTimeout = 5 * time.Minute
	
	// DefaultRequestTimeout 默认请求超时
	DefaultRequestTimeout = 30 * time.Second
)

// QPS相关常量
const (
	// Default123QPS 123网盘默认QPS
	Default123QPS = 5.0
	
	// Default115QPS 115网盘默认QPS
	Default115QPS = 4.0
	
	// DefaultOSSQPS OSS默认QPS
	DefaultOSSQPS = 10.0
)

// 并发相关常量
const (
	// DefaultMaxConcurrency 默认最大并发数
	DefaultMaxConcurrency = 4
	
	// MinConcurrency 最小并发数
	MinConcurrency = 1
	
	// MaxConcurrency 最大并发数
	MaxConcurrency = 16
)

// 缓存相关常量
const (
	// DefaultCacheExpiry 默认缓存过期时间
	DefaultCacheExpiry = 24 * time.Hour
	
	// DefaultCacheCleanupInterval 默认缓存清理间隔
	DefaultCacheCleanupInterval = 1 * time.Hour
	
	// MaxCacheEntries 最大缓存条目数
	MaxCacheEntries = 10000
)

// 日志相关常量
const (
	// LogLevelVerbose 详细日志级别
	LogLevelVerbose = "verbose"
	
	// LogLevelNormal 普通日志级别
	LogLevelNormal = "normal"
	
	// LogLevelQuiet 安静日志级别
	LogLevelQuiet = "quiet"
)

// 错误相关常量
const (
	// MaxErrorMessageLength 最大错误消息长度
	MaxErrorMessageLength = 500
	
	// DefaultErrorContext 默认错误上下文长度
	DefaultErrorContext = 100
)

// 传输相关常量
const (
	// CrossCloudTransferPrefix 跨云传输前缀
	CrossCloudTransferPrefix = "crosscloud_"
	
	// LocalTransferPrefix 本地传输前缀
	LocalTransferPrefix = "local_"
	
	// DefaultTransferTimeout 默认传输超时
	DefaultTransferTimeout = 30 * time.Minute
)

// 哈希相关常量
const (
	// MD5HashLength MD5哈希长度
	MD5HashLength = 32
	
	// SHA1HashLength SHA1哈希长度
	SHA1HashLength = 40
	
	// SHA256HashLength SHA256哈希长度
	SHA256HashLength = 64
)
