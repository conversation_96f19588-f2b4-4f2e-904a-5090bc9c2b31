// Code generated by "go run gen_setfrom.go"; DO NOT EDIT.

package s3

import (
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
)

// setFrom_s3ListObjectsInput_s3ListObjectsV2Input copies matching elements from a to b
func setFrom_s3ListObjectsInput_s3ListObjectsV2Input(a *s3.ListObjectsInput, b *s3.ListObjectsV2Input) {
	a.Bucket = b.Bucket
	a.Delimiter = b.Delimiter
	a.EncodingType = b.EncodingType
	a.ExpectedBucketOwner = b.ExpectedBucketOwner
	a.MaxKeys = b.MaxKeys
	a.OptionalObjectAttributes = b.OptionalObjectAttributes
	a.Prefix = b.Prefix
	a.RequestPayer = b.RequestPayer
}

// setFrom_s3ListObjectsV2Output_s3ListObjectsOutput copies matching elements from a to b
func setFrom_s3ListObjectsV2Output_s3ListObjectsOutput(a *s3.ListObjectsV2Output, b *s3.ListObjectsOutput) {
	a.CommonPrefixes = b.CommonPrefixes
	a.Contents = b.Contents
	a.Delimiter = b.Delimiter
	a.EncodingType = b.EncodingType
	a.IsTruncated = b.IsTruncated
	a.MaxKeys = b.MaxKeys
	a.Name = b.Name
	a.Prefix = b.Prefix
	a.RequestCharged = b.RequestCharged
	a.ResultMetadata = b.ResultMetadata
}

// setFrom_s3ListObjectVersionsInput_s3ListObjectsV2Input copies matching elements from a to b
func setFrom_s3ListObjectVersionsInput_s3ListObjectsV2Input(a *s3.ListObjectVersionsInput, b *s3.ListObjectsV2Input) {
	a.Bucket = b.Bucket
	a.Delimiter = b.Delimiter
	a.EncodingType = b.EncodingType
	a.ExpectedBucketOwner = b.ExpectedBucketOwner
	a.MaxKeys = b.MaxKeys
	a.OptionalObjectAttributes = b.OptionalObjectAttributes
	a.Prefix = b.Prefix
	a.RequestPayer = b.RequestPayer
}

// setFrom_typesObjectVersion_typesDeleteMarkerEntry copies matching elements from a to b
func setFrom_typesObjectVersion_typesDeleteMarkerEntry(a *types.ObjectVersion, b *types.DeleteMarkerEntry) {
	a.IsLatest = b.IsLatest
	a.Key = b.Key
	a.LastModified = b.LastModified
	a.Owner = b.Owner
	a.VersionId = b.VersionId
}

// setFrom_s3ListObjectsV2Output_s3ListObjectVersionsOutput copies matching elements from a to b
func setFrom_s3ListObjectsV2Output_s3ListObjectVersionsOutput(a *s3.ListObjectsV2Output, b *s3.ListObjectVersionsOutput) {
	a.CommonPrefixes = b.CommonPrefixes
	a.Delimiter = b.Delimiter
	a.EncodingType = b.EncodingType
	a.IsTruncated = b.IsTruncated
	a.MaxKeys = b.MaxKeys
	a.Name = b.Name
	a.Prefix = b.Prefix
	a.RequestCharged = b.RequestCharged
	a.ResultMetadata = b.ResultMetadata
}

// setFrom_typesObject_typesObjectVersion copies matching elements from a to b
func setFrom_typesObject_typesObjectVersion(a *types.Object, b *types.ObjectVersion) {
	a.ChecksumAlgorithm = b.ChecksumAlgorithm
	a.ETag = b.ETag
	a.Key = b.Key
	a.LastModified = b.LastModified
	a.Owner = b.Owner
	a.RestoreStatus = b.RestoreStatus
	a.Size = b.Size
}

// setFrom_s3CreateMultipartUploadInput_s3HeadObjectOutput copies matching elements from a to b
func setFrom_s3CreateMultipartUploadInput_s3HeadObjectOutput(a *s3.CreateMultipartUploadInput, b *s3.HeadObjectOutput) {
	a.BucketKeyEnabled = b.BucketKeyEnabled
	a.CacheControl = b.CacheControl
	a.ContentDisposition = b.ContentDisposition
	a.ContentEncoding = b.ContentEncoding
	a.ContentLanguage = b.ContentLanguage
	a.ContentType = b.ContentType
	a.Expires = b.Expires
	a.Metadata = b.Metadata
	a.ObjectLockLegalHoldStatus = b.ObjectLockLegalHoldStatus
	a.ObjectLockMode = b.ObjectLockMode
	a.ObjectLockRetainUntilDate = b.ObjectLockRetainUntilDate
	a.SSECustomerAlgorithm = b.SSECustomerAlgorithm
	a.SSECustomerKeyMD5 = b.SSECustomerKeyMD5
	a.SSEKMSKeyId = b.SSEKMSKeyId
	a.ServerSideEncryption = b.ServerSideEncryption
	a.StorageClass = b.StorageClass
	a.WebsiteRedirectLocation = b.WebsiteRedirectLocation
}

// setFrom_s3CreateMultipartUploadInput_s3CopyObjectInput copies matching elements from a to b
func setFrom_s3CreateMultipartUploadInput_s3CopyObjectInput(a *s3.CreateMultipartUploadInput, b *s3.CopyObjectInput) {
	a.Bucket = b.Bucket
	a.Key = b.Key
	a.ACL = b.ACL
	a.BucketKeyEnabled = b.BucketKeyEnabled
	a.CacheControl = b.CacheControl
	a.ChecksumAlgorithm = b.ChecksumAlgorithm
	a.ContentDisposition = b.ContentDisposition
	a.ContentEncoding = b.ContentEncoding
	a.ContentLanguage = b.ContentLanguage
	a.ContentType = b.ContentType
	a.ExpectedBucketOwner = b.ExpectedBucketOwner
	a.Expires = b.Expires
	a.GrantFullControl = b.GrantFullControl
	a.GrantRead = b.GrantRead
	a.GrantReadACP = b.GrantReadACP
	a.GrantWriteACP = b.GrantWriteACP
	a.Metadata = b.Metadata
	a.ObjectLockLegalHoldStatus = b.ObjectLockLegalHoldStatus
	a.ObjectLockMode = b.ObjectLockMode
	a.ObjectLockRetainUntilDate = b.ObjectLockRetainUntilDate
	a.RequestPayer = b.RequestPayer
	a.SSECustomerAlgorithm = b.SSECustomerAlgorithm
	a.SSECustomerKey = b.SSECustomerKey
	a.SSECustomerKeyMD5 = b.SSECustomerKeyMD5
	a.SSEKMSEncryptionContext = b.SSEKMSEncryptionContext
	a.SSEKMSKeyId = b.SSEKMSKeyId
	a.ServerSideEncryption = b.ServerSideEncryption
	a.StorageClass = b.StorageClass
	a.Tagging = b.Tagging
	a.WebsiteRedirectLocation = b.WebsiteRedirectLocation
}

// setFrom_s3UploadPartCopyInput_s3CopyObjectInput copies matching elements from a to b
func setFrom_s3UploadPartCopyInput_s3CopyObjectInput(a *s3.UploadPartCopyInput, b *s3.CopyObjectInput) {
	a.Bucket = b.Bucket
	a.CopySource = b.CopySource
	a.Key = b.Key
	a.CopySourceIfMatch = b.CopySourceIfMatch
	a.CopySourceIfModifiedSince = b.CopySourceIfModifiedSince
	a.CopySourceIfNoneMatch = b.CopySourceIfNoneMatch
	a.CopySourceIfUnmodifiedSince = b.CopySourceIfUnmodifiedSince
	a.CopySourceSSECustomerAlgorithm = b.CopySourceSSECustomerAlgorithm
	a.CopySourceSSECustomerKey = b.CopySourceSSECustomerKey
	a.CopySourceSSECustomerKeyMD5 = b.CopySourceSSECustomerKeyMD5
	a.ExpectedBucketOwner = b.ExpectedBucketOwner
	a.ExpectedSourceBucketOwner = b.ExpectedSourceBucketOwner
	a.RequestPayer = b.RequestPayer
	a.SSECustomerAlgorithm = b.SSECustomerAlgorithm
	a.SSECustomerKey = b.SSECustomerKey
	a.SSECustomerKeyMD5 = b.SSECustomerKeyMD5
}

// setFrom_s3HeadObjectOutput_s3GetObjectOutput copies matching elements from a to b
func setFrom_s3HeadObjectOutput_s3GetObjectOutput(a *s3.HeadObjectOutput, b *s3.GetObjectOutput) {
	a.AcceptRanges = b.AcceptRanges
	a.BucketKeyEnabled = b.BucketKeyEnabled
	a.CacheControl = b.CacheControl
	a.ChecksumCRC32 = b.ChecksumCRC32
	a.ChecksumCRC32C = b.ChecksumCRC32C
	a.ChecksumSHA1 = b.ChecksumSHA1
	a.ChecksumSHA256 = b.ChecksumSHA256
	a.ContentDisposition = b.ContentDisposition
	a.ContentEncoding = b.ContentEncoding
	a.ContentLanguage = b.ContentLanguage
	a.ContentLength = b.ContentLength
	a.ContentType = b.ContentType
	a.DeleteMarker = b.DeleteMarker
	a.ETag = b.ETag
	a.Expiration = b.Expiration
	a.Expires = b.Expires
	a.ExpiresString = b.ExpiresString
	a.LastModified = b.LastModified
	a.Metadata = b.Metadata
	a.MissingMeta = b.MissingMeta
	a.ObjectLockLegalHoldStatus = b.ObjectLockLegalHoldStatus
	a.ObjectLockMode = b.ObjectLockMode
	a.ObjectLockRetainUntilDate = b.ObjectLockRetainUntilDate
	a.PartsCount = b.PartsCount
	a.ReplicationStatus = b.ReplicationStatus
	a.RequestCharged = b.RequestCharged
	a.Restore = b.Restore
	a.SSECustomerAlgorithm = b.SSECustomerAlgorithm
	a.SSECustomerKeyMD5 = b.SSECustomerKeyMD5
	a.SSEKMSKeyId = b.SSEKMSKeyId
	a.ServerSideEncryption = b.ServerSideEncryption
	a.StorageClass = b.StorageClass
	a.VersionId = b.VersionId
	a.WebsiteRedirectLocation = b.WebsiteRedirectLocation
	a.ResultMetadata = b.ResultMetadata
}

// setFrom_s3CreateMultipartUploadInput_s3PutObjectInput copies matching elements from a to b
func setFrom_s3CreateMultipartUploadInput_s3PutObjectInput(a *s3.CreateMultipartUploadInput, b *s3.PutObjectInput) {
	a.Bucket = b.Bucket
	a.Key = b.Key
	a.ACL = b.ACL
	a.BucketKeyEnabled = b.BucketKeyEnabled
	a.CacheControl = b.CacheControl
	a.ChecksumAlgorithm = b.ChecksumAlgorithm
	a.ContentDisposition = b.ContentDisposition
	a.ContentEncoding = b.ContentEncoding
	a.ContentLanguage = b.ContentLanguage
	a.ContentType = b.ContentType
	a.ExpectedBucketOwner = b.ExpectedBucketOwner
	a.Expires = b.Expires
	a.GrantFullControl = b.GrantFullControl
	a.GrantRead = b.GrantRead
	a.GrantReadACP = b.GrantReadACP
	a.GrantWriteACP = b.GrantWriteACP
	a.Metadata = b.Metadata
	a.ObjectLockLegalHoldStatus = b.ObjectLockLegalHoldStatus
	a.ObjectLockMode = b.ObjectLockMode
	a.ObjectLockRetainUntilDate = b.ObjectLockRetainUntilDate
	a.RequestPayer = b.RequestPayer
	a.SSECustomerAlgorithm = b.SSECustomerAlgorithm
	a.SSECustomerKey = b.SSECustomerKey
	a.SSECustomerKeyMD5 = b.SSECustomerKeyMD5
	a.SSEKMSEncryptionContext = b.SSEKMSEncryptionContext
	a.SSEKMSKeyId = b.SSEKMSKeyId
	a.ServerSideEncryption = b.ServerSideEncryption
	a.StorageClass = b.StorageClass
	a.Tagging = b.Tagging
	a.WebsiteRedirectLocation = b.WebsiteRedirectLocation
}

// setFrom_s3HeadObjectOutput_s3PutObjectInput copies matching elements from a to b
func setFrom_s3HeadObjectOutput_s3PutObjectInput(a *s3.HeadObjectOutput, b *s3.PutObjectInput) {
	a.BucketKeyEnabled = b.BucketKeyEnabled
	a.CacheControl = b.CacheControl
	a.ChecksumCRC32 = b.ChecksumCRC32
	a.ChecksumCRC32C = b.ChecksumCRC32C
	a.ChecksumSHA1 = b.ChecksumSHA1
	a.ChecksumSHA256 = b.ChecksumSHA256
	a.ContentDisposition = b.ContentDisposition
	a.ContentEncoding = b.ContentEncoding
	a.ContentLanguage = b.ContentLanguage
	a.ContentLength = b.ContentLength
	a.ContentType = b.ContentType
	a.Expires = b.Expires
	a.Metadata = b.Metadata
	a.ObjectLockLegalHoldStatus = b.ObjectLockLegalHoldStatus
	a.ObjectLockMode = b.ObjectLockMode
	a.ObjectLockRetainUntilDate = b.ObjectLockRetainUntilDate
	a.SSECustomerAlgorithm = b.SSECustomerAlgorithm
	a.SSECustomerKeyMD5 = b.SSECustomerKeyMD5
	a.SSEKMSKeyId = b.SSEKMSKeyId
	a.ServerSideEncryption = b.ServerSideEncryption
	a.StorageClass = b.StorageClass
	a.WebsiteRedirectLocation = b.WebsiteRedirectLocation
}

// setFrom_s3CopyObjectInput_s3PutObjectInput copies matching elements from a to b
func setFrom_s3CopyObjectInput_s3PutObjectInput(a *s3.CopyObjectInput, b *s3.PutObjectInput) {
	a.Bucket = b.Bucket
	a.Key = b.Key
	a.ACL = b.ACL
	a.BucketKeyEnabled = b.BucketKeyEnabled
	a.CacheControl = b.CacheControl
	a.ChecksumAlgorithm = b.ChecksumAlgorithm
	a.ContentDisposition = b.ContentDisposition
	a.ContentEncoding = b.ContentEncoding
	a.ContentLanguage = b.ContentLanguage
	a.ContentType = b.ContentType
	a.ExpectedBucketOwner = b.ExpectedBucketOwner
	a.Expires = b.Expires
	a.GrantFullControl = b.GrantFullControl
	a.GrantRead = b.GrantRead
	a.GrantReadACP = b.GrantReadACP
	a.GrantWriteACP = b.GrantWriteACP
	a.Metadata = b.Metadata
	a.ObjectLockLegalHoldStatus = b.ObjectLockLegalHoldStatus
	a.ObjectLockMode = b.ObjectLockMode
	a.ObjectLockRetainUntilDate = b.ObjectLockRetainUntilDate
	a.RequestPayer = b.RequestPayer
	a.SSECustomerAlgorithm = b.SSECustomerAlgorithm
	a.SSECustomerKey = b.SSECustomerKey
	a.SSECustomerKeyMD5 = b.SSECustomerKeyMD5
	a.SSEKMSEncryptionContext = b.SSEKMSEncryptionContext
	a.SSEKMSKeyId = b.SSEKMSKeyId
	a.ServerSideEncryption = b.ServerSideEncryption
	a.StorageClass = b.StorageClass
	a.Tagging = b.Tagging
	a.WebsiteRedirectLocation = b.WebsiteRedirectLocation
}
