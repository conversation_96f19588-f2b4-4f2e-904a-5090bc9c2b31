# 123云盘 API 文档

## 概述

本文档基于 123云盘开放平台 API 文档整理，包含文件上传相关的核心接口。

## 通用参数

### Header 参数
所有接口都需要以下 Header 参数：

| 名称 | 类型 | 是否必填 | 说明 |
|------|------|----------|------|
| Authorization | string | 必填 | 鉴权access_token，格式：Bearer {token} |
| Platform | string | 必填 | 固定为：open_platform |

### 基础域名
- API 域名：`https://open-api.123pan.com`
- 上传域名：通过"获取上传域名"接口获取

## 上传流程说明

### 分片上传流程
1. **创建文件**
   - 调用创建文件接口，如果返回 `reuse` 为 true，表示秒传成功，上传结束
   - 非秒传情况返回 `preuploadID` 与分片大小 `sliceSize`，需要按此大小切分文件
   - 返回 `servers` 为后续上传文件的对应域名

2. **上传分片**
   - 按照 `sliceSize` 将文件切分，并计算每个分片的MD5
   - 调用上传分片接口，注意 Content-Type 为 `multipart/form-data`

3. **上传完毕**
   - 调用上传完毕接口，如果返回 `completed` 为 true 且 `fileID` 不为0，上传完成
   - 如果 `completed` 为 false，需间隔1秒继续轮询此接口

### 单步上传流程
1. **获取上传域名**
   - 调用该接口获取上传域名

2. **发起上传**
   - 计算文件MD5
   - 使用获取到的上传域名发起上传
   - 注意 Content-Type 为 `multipart/form-data`

## API 接口详情

### 1. 创建文件

**接口地址：** `POST {域名}/upload/v2/file/create`

**说明：**
- 文件名要小于256个字符且不能包含以下字符：`"\/:*?|><`
- 文件名不能全部是空格
- 开发者上传单文件大小限制10GB

**Body 参数：**

| 名称 | 类型 | 是否必填 | 说明 |
|------|------|----------|------|
| parentFileID | number | 必填 | 父目录id，上传到根目录时填写 0 |
| filename | string | 必填 | 文件名，不能重名。containDir 为 true 时，传入路径+文件名 |
| etag | string | 必填 | 文件md5 |
| size | number | 必填 | 文件大小，单位为 byte 字节 |
| duplicate | number | 非必填 | 文件处理策略（1保留两者，新文件名将自动添加后缀，2覆盖原文件） |
| containDir | bool | 非必填 | 上传文件是否包含路径，默认false |

**返回数据：**

| 名称 | 类型 | 是否必填 | 说明 |
|------|------|----------|------|
| fileID | number | 非必填 | 文件ID，秒传时返回，唯一 |
| preuploadID | string | 必填 | 预上传ID(如果 reuse 为 true 时,该字段不存在) |
| reuse | boolean | 必填 | 是否秒传，返回true时表示文件已上传成功 |
| sliceSize | number | 必填 | 分片大小，必须按此大小生成文件分片再上传 |
| servers | array | 必填 | 上传地址 |

### 2. 上传分片

**接口地址：** `POST {上传域名}/upload/v2/file/slice`

**说明：**
- 上传域名是创建文件接口响应中的 servers
- Content-Type: multipart/form-data

**Body 参数：**

| 名称 | 类型 | 是否必填 | 说明 |
|------|------|----------|------|
| preuploadID | string | 必填 | 预上传ID |
| sliceNo | number | 必填 | 分片序号，从1开始自增 |
| sliceMD5 | string | 必填 | 当前分片md5 |
| slice | file | 必填 | 分片二进制流 |

**返回数据：** 无特殊返回数据

### 3. 上传完毕

**接口地址：** `POST {域名}/upload/v2/file/upload_complete`

**说明：** 分片上传完成后请求

**Body 参数：**

| 名称 | 类型 | 是否必填 | 说明 |
|------|------|----------|------|
| preuploadID | string | 必填 | 预上传ID |

**返回数据：**

| 名称 | 类型 | 是否必填 | 说明 |
|------|------|----------|------|
| completed | bool | 必填 | 上传是否完成 |
| fileID | number | 必填 | 上传完成文件id |

### 4. 获取上传域名

**接口地址：** `GET {域名}/upload/v2/file/domain`

**Body 参数：** 无

**返回数据：**

| 名称 | 类型 | 是否必填 | 说明 |
|------|------|----------|------|
| data | array | 必填 | 上传域名，存在多个可以任选其一 |

### 5. 单步上传

**接口地址：** `POST {上传域名}/upload/v2/file/single/create`

**说明：**
- 文件名要小于256个字符且不能包含以下字符：`"\/:*?|><`
- 文件名不能全部是空格
- 此接口限制开发者上传单文件大小为1GB
- 上传域名是获取上传域名接口响应中的域名
- 此接口用于实现小文件单步上传一次HTTP请求交互即可完成上传

**Body 参数：**

| 名称 | 类型 | 是否必填 | 说明 |
|------|------|----------|------|
| parentFileID | number | 必填 | 父目录id，上传到根目录时填写 0 |
| filename | string | 必填 | 文件名，不能重名。containDir 为 true 时，传入路径+文件名 |
| etag | string | 必填 | 文件md5 |
| size | number | 必填 | 文件大小，单位为 byte 字节 |
| file | file | 必填 | 文件二进制流 |
| duplicate | number | 非必填 | 文件处理策略（1保留两者，新文件名将自动添加后缀，2覆盖原文件） |
| containDir | bool | 非必填 | 上传文件是否包含路径，默认false |

**返回数据：**

| 名称 | 类型 | 是否必填 | 说明 |
|------|------|----------|------|
| fileID | number | 必填 | 文件ID，秒传时返回，唯一 |
| completed | bool | 必填 | 是否上传完成（如果 completed 为 true 时，则说明上传完成） |

## 示例代码

### 创建文件示例

```bash
curl --location 'https://open-api.123pan.com/upload/v2/file/create' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer {your_token}' \
--data '{
  "parentFileID": 0,
  "filename": "测试文件.mp4",
  "etag": "4b5c549c4abd0a079caf92d6cad24127",
  "size": 50650928
}'
```

### 上传分片示例

```bash
curl --request POST \
--url http://openapi-upload.123242.com/upload/v2/file/slice \
--header 'Authorization: Bearer {your_token}' \
--header 'Platform: open_platform' \
--header 'content-type: multipart/form-data' \
--form preuploadID={preupload_id} \
--form sliceNo=1 \
--form sliceMD5=58f06dd588d8ffb3beb46ada6309436b \
--form 'slice=@/path/to/slice/file'
```

### 单步上传示例

```bash
curl --request POST \
--url http://openapi-upload.123242.com/upload/v2/file/single/create \
--header 'Authorization: Bearer {your_token}' \
--header 'Platform: open_platform' \
--header 'content-type: multipart/form-data' \
--form 'file=@/path/to/file' \
--form parentFileID=0 \
--form 'filename=测试.exe' \
--form etag=511215951b857390c3f30c17d0dae8ee \
--form size=35763200
```

## 注意事项

1. 所有接口都需要正确的 Authorization 和 Platform 头部
2. 文件上传相关接口需要使用 `multipart/form-data` 格式
3. 大文件建议使用分片上传，小文件可以使用单步上传
4. 上传域名需要通过专门的接口获取，不要硬编码
5. 分片上传需要按照返回的 sliceSize 进行切分
6. 上传完毕接口可能需要轮询等待处理完成

| 接口路径                     | QPS 限制 | 说明                                                                                                                                                                                                                                                         |
| :--------------------------- | :------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `/upload/v1/file/create`     | 5        | 此接口用于文件创建操作，限制每秒 5 次请求，避免因短时间内大量创建文件请求，对存储系统造成压力。                                                                                                                                                                  |
| `/upload/v1/file/get_upload_url` | 20       | 该接口用于获取文件上传地址，较高的 QPS 限制可满足较大并发的文件上传需求，确保用户能快速获取上传地址。                                                                                                                                                              |
| `/upload/v1/file/list_upload_parts` | 20       | 主要用于列出文件上传分片，每秒 20 次请求可满足分块上传场景下的进度查询需求。                                                                                                                                                                                  |
| `/upload/v1/file/mkdir`      | 5        | 用于创建文件夹，限制每秒 5 次请求，防止用户频繁创建文件夹，影响目录结构管理。                                                                                                                                                                                      |
| `/upload/v1/file/upload_async_result` | 5        | 获取异步上传结果的接口，因异步上传本身存在时间间隔，每秒 5 次请求可合理控制资源消耗。                                                                                                                                                                      |
| `/upload/v1/file/upload_complete` | 20       | 标志文件上传完成的接口，允许较高频率请求，以确保上传流程顺利结束。                                                                                                                                                                                           |
| `/api/v1/file/delete`        | 1        | 文件删除操作较为敏感，限制每秒 1 次请求，避免误操作或恶意批量删除，保护数据安全。                                                                                                                                                                                  |
| `/api/v1/access_token`       | 8        | 获取访问令牌的接口，每秒 8 次请求限制，防止用户频繁获取令牌，保障授权系统安全。                                                                                                                                                                                      |
| `/api/v1/file/list`          | 1        | 列出文件列表的接口，限制频率可防止用户过度获取文件列表，影响系统性能。                                                                                                                                                                                               |
| `/api/v1/file/trash`         | 5        | 将文件移入回收站的接口，限制每秒 5 次请求，避免短时间内大量文件移入回收站，消耗过多系统资源。                                                                                                                                                                      |
| `/api/v1/file/move`          | 10       | 文件移动操作的接口，每秒 10 次请求可满足用户正常的文件整理需求，同时保障系统资源合理利用。                                                                                                                                                                          |
| `/api/v1/user/info`          | 10       | 获取用户信息的接口，每秒 10 次请求限制，防止过度查询用户信息，保护用户隐私并保障系统资源。                                                                                                                                                                          |
| `/api/v2/file/list`          | 15       | 类似于`/api/v1/file/list`，但可能在功能或适用场景上有所差异，较高的 QPS 限制可平衡用户需求与系统性能。                                                                                                                                                           |
| `/api/v1/video/transcode/list` | 1        | 视频转码列表接口，由于视频转码资源消耗较大，限制每秒 1 次请求，确保转码任务合理分配资源。                                                                                                                                                                          |
| `/api/v1/file/infos`         | 10       | 用于获取文件详细信息，每秒 10 次请求可在满足用户查询需求的同时，避免对存储系统的频繁读取。                                                                                                                                                                          |
| `/api/v1/file/download_info` | 5        | 获取文件下载信息的接口，限制请求频率可防止用户过
