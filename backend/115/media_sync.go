package _115

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"

	"github.com/rclone/rclone/fs"
)

// MediaSyncStats 媒体同步统计信息
type MediaSyncStats struct {
	ProcessedDirs  int      `json:"processed_dirs"`
	ProcessedFiles int      `json:"processed_files"`
	CreatedStrm    int      `json:"created_strm"`
	SkippedFiles   int      `json:"skipped_files"`
	DeletedStrm    int      `json:"deleted_strm"`
	DeletedDirs    int      `json:"deleted_dirs"`
	Errors         int      `json:"errors"`
	ErrorMessages  []string `json:"error_messages,omitempty"`
	DryRun         bool     `json:"dry_run"`
	SyncDelete     bool     `json:"sync_delete"`
}

// mediaSyncCommand 实现媒体库同步功能
func (f *Fs) mediaSyncCommand(ctx context.Context, args []string, opt map[string]string) (interface{}, error) {
	fs.Infof(f, "🎬 开始115网盘媒体库同步...")

	// 1. 参数解析和验证
	var sourcePath, targetPath string

	// rclone backend 命令的参数解析：
	// 当使用 "rclone backend media-sync 115:/path /target" 时
	// 115:/path 会被解析为后端路径，args 中只包含 /target
	if len(args) >= 1 {
		targetPath = args[0]
		// 源路径使用当前文件系统的根路径
		sourcePath = ""
	} else {
		// 如果没有参数，检查是否通过选项指定
		if tp, ok := opt["target-path"]; ok {
			targetPath = tp
			sourcePath = ""
		} else {
			return nil, fmt.Errorf("需要提供目标路径作为参数或通过 --target-path 选项指定")
		}
	}

	// 2. 选项解析
	minSize, err := f.parseSize(opt["min-size"], "100M")
	if err != nil {
		return nil, fmt.Errorf("解析 min-size 失败: %w", err)
	}

	strmFormat := opt["strm-format"]
	if strmFormat == "" {
		strmFormat = "true"
	}

	includeExts := f.parseExtensions(opt["include"], "mp4,mkv,avi,mov,wmv,flv,webm,m4v,3gp,ts,m2ts")
	excludeExts := f.parseExtensions(opt["exclude"], "")

	dryRun := opt["dry-run"] == "true"
	// media-sync 默认启用同步删除，类似 rclone sync
	syncDelete := true
	// 如果用户明确设置为 false，则禁用同步删除
	if opt["sync-delete"] == "false" {
		syncDelete = false
	}

	// 3. 初始化统计信息
	stats := &MediaSyncStats{
		DryRun:     dryRun,
		SyncDelete: syncDelete,
	}

	fs.Infof(f, "📋 同步参数: 源=%s, 目标=%s, 最小大小=%s, 格式=%s, 预览=%v, 同步删除=%v",
		sourcePath, targetPath, fs.SizeSuffix(minSize), strmFormat, dryRun, syncDelete)

	// 4. 开始递归处理
	// 获取源路径的根目录名称，并添加到目标路径中
	rootDirName := f.root
	if rootDirName == "" {
		rootDirName = "root"
	}
	// 清理路径，去掉末尾的斜杠
	rootDirName = strings.TrimSuffix(rootDirName, "/")

	// 构建包含根目录的目标路径
	fullTargetPath := filepath.Join(targetPath, rootDirName)

	err = f.processDirectoryForMediaSync(ctx, sourcePath, fullTargetPath, minSize, strmFormat,
		includeExts, excludeExts, stats)
	if err != nil {
		return stats, fmt.Errorf("媒体同步失败: %w", err)
	}

	// 5. 如果启用了同步删除，进行全局清理
	if stats.SyncDelete {
		fs.Infof(f, "🧹 开始全局同步删除...")
		err := f.globalSyncDelete115(ctx, sourcePath, targetPath, includeExts, excludeExts, stats)
		if err != nil {
			fs.Logf(f, "⚠️ 全局同步删除失败: %v", err)
			// 不中断整个过程，继续执行
		}
	}

	if stats.SyncDelete {
		if stats.DeletedDirs > 0 {
			fs.Infof(f, "🎉 媒体同步完成! 处理目录:%d, 处理文件:%d, 创建.strm:%d, 删除.strm:%d, 删除目录:%d, 跳过:%d, 错误:%d",
				stats.ProcessedDirs, stats.ProcessedFiles, stats.CreatedStrm, stats.DeletedStrm, stats.DeletedDirs, stats.SkippedFiles, stats.Errors)
		} else {
			fs.Infof(f, "🎉 媒体同步完成! 处理目录:%d, 处理文件:%d, 创建.strm:%d, 删除.strm:%d, 跳过:%d, 错误:%d",
				stats.ProcessedDirs, stats.ProcessedFiles, stats.CreatedStrm, stats.DeletedStrm, stats.SkippedFiles, stats.Errors)
		}
	} else {
		fs.Infof(f, "🎉 媒体同步完成! 处理目录:%d, 处理文件:%d, 创建.strm:%d, 跳过:%d, 错误:%d",
			stats.ProcessedDirs, stats.ProcessedFiles, stats.CreatedStrm, stats.SkippedFiles, stats.Errors)
	}

	return stats, nil
}

// parseSize 解析大小字符串
func (f *Fs) parseSize(sizeStr, defaultSize string) (int64, error) {
	if sizeStr == "" {
		sizeStr = defaultSize
	}
	sizeSuffix := fs.SizeSuffix(0)
	err := sizeSuffix.Set(sizeStr)
	if err != nil {
		return 0, err
	}
	return int64(sizeSuffix), nil
}

// parseExtensions 解析文件扩展名列表
func (f *Fs) parseExtensions(extStr, defaultExts string) map[string]bool {
	if extStr == "" {
		extStr = defaultExts
	}

	extMap := make(map[string]bool)
	if extStr != "" {
		exts := strings.Split(extStr, ",")
		for _, ext := range exts {
			ext = strings.TrimSpace(strings.ToLower(ext))
			if ext != "" {
				// 确保扩展名以点开头
				if !strings.HasPrefix(ext, ".") {
					ext = "." + ext
				}
				extMap[ext] = true
			}
		}
	}
	return extMap
}

// isVideoFile 检查是否为视频文件
func (f *Fs) isVideoFile(filename string, includeExts, excludeExts map[string]bool) bool {
	ext := strings.ToLower(filepath.Ext(filename))

	// 检查排除列表
	if len(excludeExts) > 0 && excludeExts[ext] {
		return false
	}

	// 检查包含列表
	if len(includeExts) > 0 {
		return includeExts[ext]
	}

	// 默认视频扩展名
	defaultVideoExts := map[string]bool{
		".mp4": true, ".mkv": true, ".avi": true, ".mov": true,
		".wmv": true, ".flv": true, ".webm": true, ".m4v": true,
		".3gp": true, ".ts": true, ".m2ts": true,
	}
	return defaultVideoExts[ext]
}

// processDirectoryForMediaSync 递归处理目录进行媒体同步
func (f *Fs) processDirectoryForMediaSync(ctx context.Context, sourcePath, targetPath string,
	minSize int64, strmFormat string, includeExts, excludeExts map[string]bool, stats *MediaSyncStats) error {

	fs.Debugf(f, "📁 处理目录: %s -> %s", sourcePath, targetPath)
	stats.ProcessedDirs++

	// 1. 创建目标目录
	if !stats.DryRun {
		if err := os.MkdirAll(targetPath, 0755); err != nil {
			errMsg := fmt.Sprintf("创建目录失败 %s: %v", targetPath, err)
			stats.ErrorMessages = append(stats.ErrorMessages, errMsg)
			stats.Errors++
			return fmt.Errorf(errMsg)
		}
	} else {
		fs.Infof(f, "🔍 [预览] 将创建目录: %s", targetPath)
	}

	// 2. 列出源目录内容
	entries, err := f.List(ctx, sourcePath)
	if err != nil {
		errMsg := fmt.Sprintf("列出目录失败 %s: %v", sourcePath, err)
		stats.ErrorMessages = append(stats.ErrorMessages, errMsg)
		stats.Errors++
		return fmt.Errorf(errMsg)
	}

	// 3. 处理每个条目
	for _, entry := range entries {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		switch e := entry.(type) {
		case fs.Directory:
			// 递归处理子目录
			// e.Remote() 返回相对于文件系统根的完整路径
			// 我们需要计算相对于当前 sourcePath 的路径
			relativePath := e.Remote()
			if sourcePath != "" {
				// 如果有源路径前缀，去掉它来获取相对路径
				if strings.HasPrefix(relativePath, sourcePath+"/") {
					relativePath = strings.TrimPrefix(relativePath, sourcePath+"/")
				} else if relativePath == sourcePath {
					// 如果完全匹配，说明这是当前目录本身，跳过
					continue
				}
			}

			subSourcePath := e.Remote() // 使用完整路径作为源路径
			subTargetPath := filepath.Join(targetPath, relativePath)

			err := f.processDirectoryForMediaSync(ctx, subSourcePath, subTargetPath,
				minSize, strmFormat, includeExts, excludeExts, stats)
			if err != nil {
				fs.Logf(f, "⚠️ 处理子目录失败: %v", err)
				// 继续处理其他目录，不中断整个过程
			}

		case fs.Object:
			// 处理文件
			stats.ProcessedFiles++

			// 检查文件类型和大小
			fileName := filepath.Base(e.Remote())
			if !f.isVideoFile(fileName, includeExts, excludeExts) {
				fs.Debugf(f, "⏭️ 跳过非视频文件: %s", fileName)
				stats.SkippedFiles++
				continue
			}

			if e.Size() < minSize {
				fs.Debugf(f, "⏭️ 跳过小文件 (%s): %s", fs.SizeSuffix(e.Size()), fileName)
				stats.SkippedFiles++
				continue
			}

			// 创建 .strm 文件
			err := f.createStrmFileFor115(ctx, e, targetPath, strmFormat, stats)
			if err != nil {
				errMsg := fmt.Sprintf("创建.strm文件失败 %s: %v", fileName, err)
				stats.ErrorMessages = append(stats.ErrorMessages, errMsg)
				stats.Errors++
				fs.Logf(f, "❌ %s", errMsg)
			} else {
				stats.CreatedStrm++
			}
		}
	}

	// 4. 如果启用了同步删除，检查并删除本地不存在于网盘的.strm文件
	if stats.SyncDelete {
		err := f.cleanupOrphanedStrmFiles115(ctx, targetPath, entries, includeExts, excludeExts, stats)
		if err != nil {
			fs.Logf(f, "⚠️ 清理孤立.strm文件失败: %v", err)
			// 不中断整个过程，继续执行
		}
	}

	return nil
}

// createStrmFileFor115 为115网盘文件创建.strm文件
func (f *Fs) createStrmFileFor115(ctx context.Context, obj fs.Object, targetDir, strmFormat string, stats *MediaSyncStats) error {
	// 1. 生成 .strm 文件路径
	fileName := filepath.Base(obj.Remote()) // 只使用文件名，不包含路径
	baseName := strings.TrimSuffix(fileName, filepath.Ext(fileName))
	strmPath := filepath.Join(targetDir, baseName+".strm")

	// 2. 生成 .strm 文件内容
	var content string
	switch strmFormat {
	case "true":
		// 使用优化格式：115://pick_code
		if o, ok := obj.(*Object); ok && o.pickCode != "" {
			content = fmt.Sprintf("115://%s\n", o.pickCode)
		} else {
			// 如果无法从对象获取pick_code，尝试通过路径获取
			pickCode, err := f.GetPickCodeByPath(ctx, obj.Remote())
			if err == nil && pickCode != "" {
				content = fmt.Sprintf("115://%s\n", pickCode)
			} else {
				// 如果无法获取pick_code，回退到路径模式
				fs.Debugf(f, "⚠️ 无法获取pick_code，使用路径模式: %s", obj.Remote())
				content = obj.Remote() + "\n"
			}
		}
	case "false":
		// 使用路径格式
		content = obj.Remote() + "\n"
	case "fileid":
		// 兼容旧格式名称
		if o, ok := obj.(*Object); ok && o.pickCode != "" {
			content = fmt.Sprintf("115://%s\n", o.pickCode)
		} else {
			pickCode, err := f.GetPickCodeByPath(ctx, obj.Remote())
			if err == nil && pickCode != "" {
				content = fmt.Sprintf("115://%s\n", pickCode)
			} else {
				fs.Debugf(f, "⚠️ 无法获取pick_code，使用路径模式: %s", obj.Remote())
				content = obj.Remote() + "\n"
			}
		}
	case "pickcode":
		// 兼容旧格式名称
		if o, ok := obj.(*Object); ok && o.pickCode != "" {
			content = fmt.Sprintf("115://%s\n", o.pickCode)
		} else {
			pickCode, err := f.GetPickCodeByPath(ctx, obj.Remote())
			if err == nil && pickCode != "" {
				content = fmt.Sprintf("115://%s\n", pickCode)
			} else {
				fs.Debugf(f, "⚠️ 无法获取pick_code，使用路径模式: %s", obj.Remote())
				content = obj.Remote() + "\n"
			}
		}
	case "path":
		// 兼容模式：使用文件路径
		content = obj.Remote() + "\n"
	default:
		// 默认使用优化格式
		if o, ok := obj.(*Object); ok && o.pickCode != "" {
			content = fmt.Sprintf("115://%s\n", o.pickCode)
		} else {
			// 尝试通过路径获取pick_code
			pickCode, err := f.GetPickCodeByPath(ctx, obj.Remote())
			if err == nil && pickCode != "" {
				content = fmt.Sprintf("115://%s\n", pickCode)
			} else {
				content = obj.Remote() + "\n"
			}
		}
	}

	// 3. 检查是否为预览模式
	if stats.DryRun {
		fs.Infof(f, "🔍 [预览] 将创建.strm文件: %s (内容: %s)", strmPath, strings.TrimSpace(content))
		return nil
	}

	// 4. 检查文件是否已存在
	if _, err := os.Stat(strmPath); err == nil {
		fs.Debugf(f, "📄 .strm文件已存在，将覆盖: %s", strmPath)
	}

	// 5. 创建 .strm 文件
	file, err := os.Create(strmPath)
	if err != nil {
		return fmt.Errorf("创建.strm文件失败: %w", err)
	}
	defer file.Close()

	_, err = file.WriteString(content)
	if err != nil {
		return fmt.Errorf("写入.strm文件失败: %w", err)
	}

	fs.Infof(f, "✅ 创建.strm文件: %s (大小: %s, 内容: %s)",
		strmPath, fs.SizeSuffix(obj.Size()), strings.TrimSpace(content))

	return nil
}

// cleanupOrphanedStrmFiles115 清理本地不存在于网盘的.strm文件
func (f *Fs) cleanupOrphanedStrmFiles115(ctx context.Context, targetPath string, cloudEntries []fs.DirEntry,
	includeExts, excludeExts map[string]bool, stats *MediaSyncStats) error {

	fs.Debugf(f, "🧹 开始清理孤立的.strm文件: %s", targetPath)

	// 1. 读取本地目录中的所有.strm文件
	localStrmFiles, err := filepath.Glob(filepath.Join(targetPath, "*.strm"))
	if err != nil {
		return fmt.Errorf("读取本地.strm文件失败: %w", err)
	}

	// 如果当前目录没有.strm文件，检查父目录
	if len(localStrmFiles) == 0 {
		parentDir := filepath.Dir(targetPath)
		if parentDir != targetPath && parentDir != "." && parentDir != "/" {
			fs.Debugf(f, "📂 当前目录中没有.strm文件，检查父目录: %s -> %s", targetPath, parentDir)
			parentStrmFiles, err := filepath.Glob(filepath.Join(parentDir, "*.strm"))
			if err != nil {
				fs.Debugf(f, "⚠️ 读取父目录.strm文件失败: %v", err)
			} else if len(parentStrmFiles) > 0 {
				fs.Debugf(f, "📂 在父目录中找到%d个.strm文件，使用父目录进行清理", len(parentStrmFiles))
				localStrmFiles = parentStrmFiles
				targetPath = parentDir // 更新目标路径为父目录
			}
		}
	}

	if len(localStrmFiles) == 0 {
		fs.Debugf(f, "📂 目录中没有.strm文件: %s", targetPath)
		return nil
	}

	// 2. 构建网盘中视频文件的映射（文件名 -> 是否存在）
	cloudVideoFiles := make(map[string]bool)
	for _, entry := range cloudEntries {
		if obj, ok := entry.(fs.Object); ok {
			fileName := filepath.Base(obj.Remote())
			if f.isVideoFile(fileName, includeExts, excludeExts) {
				// 生成对应的.strm文件名
				baseName := strings.TrimSuffix(fileName, filepath.Ext(fileName))
				strmName := baseName + ".strm"
				cloudVideoFiles[strmName] = true
			}
		}
	}

	// 3. 检查每个本地.strm文件是否对应网盘中的视频文件
	for _, strmFile := range localStrmFiles {
		strmName := filepath.Base(strmFile)

		// 检查对应的视频文件是否还在网盘中
		if !cloudVideoFiles[strmName] {
			// 这个.strm文件对应的视频文件已经不在网盘中了
			if stats.DryRun {
				fs.Infof(f, "🔍 [预览] 将删除孤立的.strm文件: %s", strmFile)
			} else {
				fs.Infof(f, "🗑️ 删除孤立的.strm文件: %s", strmFile)
				if err := os.Remove(strmFile); err != nil {
					errMsg := fmt.Sprintf("删除.strm文件失败 %s: %v", strmFile, err)
					stats.ErrorMessages = append(stats.ErrorMessages, errMsg)
					stats.Errors++
					fs.Logf(f, "❌ %s", errMsg)
					continue
				}
			}
			stats.DeletedStrm++
		}
	}

	if stats.DeletedStrm > 0 {
		fs.Debugf(f, "✅ 清理完成，删除了 %d 个孤立的.strm文件", stats.DeletedStrm)

		// 清理空目录
		err := f.cleanupEmptyDirectories115(ctx, targetPath, stats)
		if err != nil {
			fs.Logf(f, "⚠️ 清理空目录失败: %v", err)
		}
	}

	return nil
}

// cleanupEmptyDirectories115 清理空目录
func (f *Fs) cleanupEmptyDirectories115(ctx context.Context, startPath string, stats *MediaSyncStats) error {
	fs.Debugf(f, "🗂️ 开始清理空目录: %s", startPath)

	// 递归清理空目录，从最深层开始
	return f.cleanupEmptyDirectoriesRecursive115(ctx, startPath, stats, 0)
}

// cleanupEmptyDirectoriesRecursive115 递归清理空目录
func (f *Fs) cleanupEmptyDirectoriesRecursive115(ctx context.Context, dirPath string, stats *MediaSyncStats, depth int) error {
	// 防止无限递归，最多向上清理5层
	if depth > 5 {
		return nil
	}

	// 检查目录是否存在
	if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		return nil
	}

	// 读取目录内容
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		return fmt.Errorf("读取目录失败 %s: %w", dirPath, err)
	}

	// 如果目录不为空，不删除
	if len(entries) > 0 {
		fs.Debugf(f, "📁 目录不为空，保留: %s (%d个项目)", dirPath, len(entries))
		return nil
	}

	// 目录为空，检查是否应该删除
	// 不删除根目录和用户指定的主要目录
	if f.shouldPreserveDirectory115(dirPath) {
		fs.Debugf(f, "🔒 保护目录，不删除: %s", dirPath)
		return nil
	}

	// 删除空目录
	if stats.DryRun {
		fs.Infof(f, "🔍 [预览] 将删除空目录: %s", dirPath)
	} else {
		fs.Infof(f, "🗑️ 删除空目录: %s", dirPath)
		if err := os.Remove(dirPath); err != nil {
			errMsg := fmt.Sprintf("删除空目录失败 %s: %v", dirPath, err)
			stats.ErrorMessages = append(stats.ErrorMessages, errMsg)
			stats.Errors++
			fs.Logf(f, "❌ %s", errMsg)
			return nil // 不中断整个过程
		}
	}
	stats.DeletedDirs++

	// 递归检查父目录
	parentDir := filepath.Dir(dirPath)
	if parentDir != dirPath && parentDir != "." && parentDir != "/" {
		return f.cleanupEmptyDirectoriesRecursive115(ctx, parentDir, stats, depth+1)
	}

	return nil
}

// shouldPreserveDirectory115 检查是否应该保护目录不被删除
func (f *Fs) shouldPreserveDirectory115(dirPath string) bool {
	// 不删除根目录
	if dirPath == "/" || dirPath == "." {
		return true
	}

	// 不删除用户主目录相关路径
	if strings.Contains(dirPath, "/home/") || strings.Contains(dirPath, "/Users/") {
		// 只有在路径很深的情况下才允许删除
		parts := strings.Split(dirPath, "/")
		if len(parts) < 5 { // 至少要有 /Users/<USER>/some/deep/path
			return true
		}
	}

	// 不删除系统重要目录
	systemDirs := []string{"/bin", "/usr", "/etc", "/var", "/opt", "/tmp"}
	for _, sysDir := range systemDirs {
		if strings.HasPrefix(dirPath, sysDir) && len(strings.Split(dirPath, "/")) < 4 {
			return true
		}
	}

	return false
}

// globalSyncDelete115 全局同步删除功能，类似 rclone sync
func (f *Fs) globalSyncDelete115(ctx context.Context, sourcePath, targetPath string,
	includeExts, excludeExts map[string]bool, stats *MediaSyncStats) error {

	fs.Debugf(f, "🧹 开始全局同步删除: %s", targetPath)

	// 1. 递归收集所有本地.strm文件
	localStrmFiles := make(map[string]string) // 相对路径 -> 绝对路径
	err := f.collectLocalStrmFiles115(targetPath, "", localStrmFiles)
	if err != nil {
		return fmt.Errorf("收集本地.strm文件失败: %w", err)
	}

	if len(localStrmFiles) == 0 {
		fs.Debugf(f, "📂 没有找到.strm文件: %s", targetPath)
		return nil
	}

	fs.Debugf(f, "📂 找到 %d 个本地.strm文件", len(localStrmFiles))

	// 2. 递归收集网盘中的所有视频文件
	cloudVideoFiles := make(map[string]bool) // .strm文件名 -> 是否存在
	err = f.collectCloudVideoFiles115(ctx, sourcePath, "", includeExts, excludeExts, cloudVideoFiles)
	if err != nil {
		return fmt.Errorf("收集网盘视频文件失败: %w", err)
	}

	fs.Debugf(f, "📂 找到 %d 个网盘视频文件", len(cloudVideoFiles))

	// 3. 找出孤立的.strm文件
	orphanedFiles := make([]string, 0)
	for relativePath, absolutePath := range localStrmFiles {
		strmName := filepath.Base(relativePath)
		if !cloudVideoFiles[strmName] {
			orphanedFiles = append(orphanedFiles, absolutePath)
		}
	}

	// 4. 删除孤立的.strm文件
	for _, strmFile := range orphanedFiles {
		if stats.DryRun {
			fs.Infof(f, "🔍 [预览] 将删除孤立的.strm文件: %s", strmFile)
		} else {
			fs.Infof(f, "🗑️ 删除孤立的.strm文件: %s", strmFile)
			if err := os.Remove(strmFile); err != nil {
				errMsg := fmt.Sprintf("删除.strm文件失败 %s: %v", strmFile, err)
				stats.ErrorMessages = append(stats.ErrorMessages, errMsg)
				stats.Errors++
				fs.Logf(f, "❌ %s", errMsg)
				continue
			}
		}
		stats.DeletedStrm++
	}

	// 5. 清理空目录
	if stats.DeletedStrm > 0 {
		fs.Debugf(f, "✅ 删除了 %d 个孤立的.strm文件，开始清理空目录", stats.DeletedStrm)
		err := f.cleanupEmptyDirectoriesGlobal115(ctx, targetPath, stats)
		if err != nil {
			fs.Logf(f, "⚠️ 清理空目录失败: %v", err)
		}
	}

	return nil
}

// collectLocalStrmFiles115 递归收集本地目录中的所有.strm文件
func (f *Fs) collectLocalStrmFiles115(basePath, relativePath string, strmFiles map[string]string) error {
	currentPath := filepath.Join(basePath, relativePath)

	entries, err := os.ReadDir(currentPath)
	if err != nil {
		return fmt.Errorf("读取目录失败 %s: %w", currentPath, err)
	}

	for _, entry := range entries {
		entryPath := filepath.Join(relativePath, entry.Name())
		fullPath := filepath.Join(basePath, entryPath)

		if entry.IsDir() {
			// 递归处理子目录
			err := f.collectLocalStrmFiles115(basePath, entryPath, strmFiles)
			if err != nil {
				fs.Debugf(f, "⚠️ 处理子目录失败: %v", err)
				// 继续处理其他目录
			}
		} else if strings.HasSuffix(entry.Name(), ".strm") {
			// 收集.strm文件
			strmFiles[entryPath] = fullPath
		}
	}

	return nil
}

// collectCloudVideoFiles115 递归收集网盘中的所有视频文件
func (f *Fs) collectCloudVideoFiles115(ctx context.Context, basePath, relativePath string,
	includeExts, excludeExts map[string]bool, videoFiles map[string]bool) error {

	currentPath := filepath.Join(basePath, relativePath)
	if currentPath == "." {
		currentPath = ""
	}

	entries, err := f.List(ctx, currentPath)
	if err != nil {
		return fmt.Errorf("列出目录失败 %s: %w", currentPath, err)
	}

	for _, entry := range entries {
		switch e := entry.(type) {
		case fs.Directory:
			// 递归处理子目录
			dirName := filepath.Base(e.Remote())
			subRelativePath := filepath.Join(relativePath, dirName)
			err := f.collectCloudVideoFiles115(ctx, basePath, subRelativePath, includeExts, excludeExts, videoFiles)
			if err != nil {
				fs.Debugf(f, "⚠️ 处理子目录失败: %v", err)
				// 继续处理其他目录
			}
		case fs.Object:
			// 检查是否为视频文件
			fileName := filepath.Base(e.Remote())
			if f.isVideoFile(fileName, includeExts, excludeExts) {
				// 生成对应的.strm文件名
				baseName := strings.TrimSuffix(fileName, filepath.Ext(fileName))
				strmName := baseName + ".strm"
				videoFiles[strmName] = true
			}
		}
	}

	return nil
}

// cleanupEmptyDirectoriesGlobal115 全局清理空目录，类似 rclone sync
func (f *Fs) cleanupEmptyDirectoriesGlobal115(ctx context.Context, targetPath string, stats *MediaSyncStats) error {
	fs.Debugf(f, "🗂️ 开始全局清理空目录: %s", targetPath)

	// 收集所有目录
	allDirs := make([]string, 0)
	err := f.collectAllDirectories115(targetPath, &allDirs)
	if err != nil {
		return fmt.Errorf("收集目录失败: %w", err)
	}

	// 按路径长度排序，从最深的开始删除（类似 rclone sync）
	sort.Slice(allDirs, func(i, j int) bool {
		return len(allDirs[i]) > len(allDirs[j])
	})

	// 删除空目录
	for _, dirPath := range allDirs {
		// 检查目录是否为空
		entries, err := os.ReadDir(dirPath)
		if err != nil {
			fs.Debugf(f, "⚠️ 读取目录失败: %s: %v", dirPath, err)
			continue
		}

		if len(entries) == 0 {
			// 目录为空，检查是否应该删除
			if f.shouldPreserveDirectory115(dirPath) {
				fs.Debugf(f, "🔒 保护目录，不删除: %s", dirPath)
				continue
			}

			// 删除空目录
			if stats.DryRun {
				fs.Infof(f, "🔍 [预览] 将删除空目录: %s", dirPath)
			} else {
				fs.Infof(f, "🗑️ 删除空目录: %s", dirPath)
				if err := os.Remove(dirPath); err != nil {
					errMsg := fmt.Sprintf("删除空目录失败 %s: %v", dirPath, err)
					stats.ErrorMessages = append(stats.ErrorMessages, errMsg)
					stats.Errors++
					fs.Logf(f, "❌ %s", errMsg)
					continue
				}
			}
			stats.DeletedDirs++
		}
	}

	if stats.DeletedDirs > 0 {
		fs.Debugf(f, "✅ 全局清理完成，删除了 %d 个空目录", stats.DeletedDirs)
	}

	return nil
}

// collectAllDirectories115 递归收集所有目录
func (f *Fs) collectAllDirectories115(basePath string, dirs *[]string) error {
	entries, err := os.ReadDir(basePath)
	if err != nil {
		return fmt.Errorf("读取目录失败 %s: %w", basePath, err)
	}

	for _, entry := range entries {
		if entry.IsDir() {
			dirPath := filepath.Join(basePath, entry.Name())
			*dirs = append(*dirs, dirPath)

			// 递归收集子目录
			err := f.collectAllDirectories115(dirPath, dirs)
			if err != nil {
				fs.Debugf(f, "⚠️ 收集子目录失败: %v", err)
				// 继续处理其他目录
			}
		}
	}

	return nil
}
